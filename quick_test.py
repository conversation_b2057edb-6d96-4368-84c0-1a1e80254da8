#!/usr/bin/env python3
"""
Quick Test of EMA System
========================
"""

import sys
import os
from datetime import datetime, timedelta

# Add src to path
sys.path.insert(0, 'src')

def main():
    print("🧪 Quick EMA System Test")
    print("=" * 30)
    
    try:
        # Test imports
        print("1. Testing imports...")
        from core.ema import EMACalculator
        from data.logger import SignalLogger
        print("   ✅ Imports successful")
        
        # Test EMA calculator
        print("2. Testing EMA Calculator...")
        calc = EMACalculator([{"short_ema": 5, "long_ema": 10}])
        print("   ✅ EMA Calculator created")
        
        # Test with sample data
        print("3. Testing with sample data...")
        prices = [24500, 24510, 24505, 24515, 24520, 24525, 24530, 24535, 24540, 24545]
        
        for i, price in enumerate(prices):
            timestamp = datetime.now() + timedelta(minutes=i)
            emas = calc.add_price("1min", price, timestamp)
            
            if i >= 9:  # After enough data
                print(f"   Price: {price}, EMAs: {emas}")
                signals = calc.get_crossover_signals("1min")
                if signals:
                    print(f"   🔔 Signals: {len(signals)}")
                    for signal in signals:
                        print(f"      {signal['signal']} - EMA5: {signal['short_value']:.2f}, EMA10: {signal['long_value']:.2f}")
        
        print("4. Testing Signal Logger...")
        os.makedirs("test_output", exist_ok=True)
        logger = SignalLogger("test_output")
        print("   ✅ Signal Logger created")
        
        # Test logging a signal
        test_signal = {
            'datetime': datetime.now(),
            'action': 'BUY',
            'price': 24550.0,
            'ohlc': {'open': 24545, 'high': 24555, 'low': 24540, 'close': 24550, 'volume': 1000},
            'short_ema_value': 24548.0,
            'long_ema_value': 24545.0,
            'pnl': 0.0
        }
        logger.log_signal(test_signal)
        logger.close()
        print("   ✅ Signal logged successfully")
        
        print("\n🎉 All tests passed! System is working correctly.")
        return True
        
    except Exception as e:
        print(f"\n❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    main()
