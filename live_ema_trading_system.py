#!/usr/bin/env python3
"""
Live EMA Crossover Trading System with DhanHQ WebSocket
======================================================

A professional-grade live trading system that:
1. Connects to DhanHQ WebSocket for real-time NIFTY 50 data
2. Calculates EMA 5 and 10 using pandas_ta on-the-fly
3. Detects bullish/bearish crossover signals with enhanced validation
4. Logs all data and signals to CSV for tracking and backtesting
5. Handles reconnections and errors gracefully

Requirements:
- DhanHQ account with API credentials
- Virtual environment with required packages
- Internet connection for live data

Usage:
    source stock/bin/activate
    python live_ema_trading_system.py

Author: AI Assistant
Date: 2025
"""

import asyncio
import json
import csv
import logging
import os
import sys
import time
import traceback
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Deque
from collections import deque
import nest_asyncio

# Enable nested asyncio for Jupyter compatibility
nest_asyncio.apply()

# Add src to path for importing our modules
sys.path.insert(0, 'src')

# Import required libraries
try:
    import pandas as pd
    import pandas_ta as ta
    import numpy as np
    from dhanhq import dhanhq
    import websockets
    from dotenv import load_dotenv
    DEPENDENCIES_AVAILABLE = True
except ImportError as e:
    print(f"❌ Missing dependencies: {e}")
    print("Install with: uv pip install dhanhq pandas-ta nest-asyncio websockets python-dotenv")
    DEPENDENCIES_AVAILABLE = False
    sys.exit(1)

# Import our existing modules
try:
    from core.ema import EMACalculator
    from data.logger import SignalLogger
    from utils.market_hours import MarketHoursManager
    MODULES_AVAILABLE = True
except ImportError as e:
    print(f"⚠️  Warning: Could not import custom modules: {e}")
    print("Will use built-in EMA calculation")
    MODULES_AVAILABLE = False


class LiveEMATrader:
    """
    Live EMA Crossover Trading System using DhanHQ WebSocket

    Features:
    - Real-time NIFTY 50 data streaming
    - EMA 5/10 crossover detection
    - Enhanced signal validation
    - CSV logging for backtesting
    - Graceful error handling and reconnection
    """

    def __init__(self, config_file: str = "config/config.json"):
        """Initialize the live trading system"""
        # Load configuration and environment
        load_dotenv()
        self.config = self.load_config(config_file)
        self.setup_logging()

        # DhanHQ credentials
        self.client_id = os.getenv('DHAN_CLIENT_ID')
        self.access_token = os.getenv('DHAN_ACCESS_TOKEN')

        if not self.client_id or not self.access_token:
            raise ValueError("DhanHQ credentials not found. Set DHAN_CLIENT_ID and DHAN_ACCESS_TOKEN in .env")

        # Initialize DhanHQ client
        self.dhan = dhanhq(self.client_id, self.access_token)

        # Market data storage
        self.price_data: Deque[Dict] = deque(maxlen=2880)  # 2 days of 1-min data
        self.ema_data: Deque[Dict] = deque(maxlen=2880)

        # EMA calculation setup
        self.ema_periods = [5, 10]
        self.min_data_points = 20  # Minimum data for reliable EMA

        # Signal tracking
        self.last_signal = None
        self.last_signal_time = None
        self.min_signal_gap = 300  # 5 minutes between signals

        # WebSocket connection
        self.ws_connection = None
        self.is_connected = False
        self.reconnect_attempts = 0
        self.max_reconnect_attempts = 10

        # CSV logging
        self.csv_file = f"data/live_ema_signals_{datetime.now().strftime('%Y%m%d')}.csv"
        self.setup_csv_logging()

        # Statistics
        self.stats = {
            'ticks_received': 0,
            'signals_generated': 0,
            'start_time': datetime.now(),
            'last_tick_time': None
        }

        # Market hours (optional)
        if MODULES_AVAILABLE:
            try:
                self.market_hours = MarketHoursManager(
                    config=self.config.get('market_hours', {}),
                    data_directory=self.config['data_directory']
                )
            except:
                self.market_hours = None
        else:
            self.market_hours = None

        self.logger.info("🚀 Live EMA Trading System initialized")
        self.logger.info(f"📊 Instrument: {self.config['instrument']['name']}")
        self.logger.info(f"📈 EMA Periods: {self.ema_periods}")
        self.logger.info(f"📁 CSV File: {self.csv_file}")

    def load_config(self, config_file: str) -> Dict:
        """Load configuration from JSON file"""
        try:
            with open(config_file, 'r') as f:
                return json.load(f)
        except Exception as e:
            # Default configuration
            return {
                "instrument": {
                    "name": "NIFTY 50",
                    "security_id": "13",
                    "exchange_segment": "IDX_I"
                },
                "data_directory": "data",
                "market_hours": {
                    "timezone": "Asia/Kolkata",
                    "start_time": "09:15",
                    "end_time": "15:15"
                }
            }

    def setup_logging(self):
        """Setup logging configuration"""
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(f'logs/live_trading_{datetime.now().strftime("%Y%m%d")}.log'),
                logging.StreamHandler()
            ]
        )
        self.logger = logging.getLogger(__name__)

    def setup_csv_logging(self):
        """Setup CSV file for logging signals"""
        os.makedirs(os.path.dirname(self.csv_file), exist_ok=True)

        # Create CSV with headers if it doesn't exist
        if not os.path.exists(self.csv_file):
            with open(self.csv_file, 'w', newline='') as f:
                writer = csv.writer(f)
                writer.writerow([
                    'timestamp', 'price', 'ema5', 'ema10', 'signal_type',
                    'crossover_strength', 'volume', 'data_points'
                ])
            self.logger.info(f"📁 Created CSV file: {self.csv_file}")

    def calculate_emas_pandas_ta(self, prices: List[float]) -> Dict[int, float]:
        """
        Calculate EMAs using pandas_ta

        Args:
            prices: List of price values

        Returns:
            Dictionary of {period: ema_value}
        """
        try:
            if len(prices) < max(self.ema_periods):
                return {}

            # Convert to pandas Series
            price_series = pd.Series(prices)

            # Calculate EMAs using pandas_ta
            emas = {}
            for period in self.ema_periods:
                ema_series = ta.ema(price_series, length=period)
                if not ema_series.empty and not pd.isna(ema_series.iloc[-1]):
                    emas[period] = float(ema_series.iloc[-1])

            return emas

        except Exception as e:
            self.logger.error(f"Error calculating EMAs with pandas_ta: {e}")
            return self.calculate_emas_fallback(prices)

    def calculate_emas_fallback(self, prices: List[float]) -> Dict[int, float]:
        """
        Fallback EMA calculation without pandas_ta

        Args:
            prices: List of price values

        Returns:
            Dictionary of {period: ema_value}
        """
        try:
            emas = {}

            for period in self.ema_periods:
                if len(prices) >= period:
                    # Calculate EMA using exponential smoothing
                    multiplier = 2.0 / (period + 1)

                    # Initialize with SMA
                    sma = sum(prices[:period]) / period
                    ema = sma

                    # Calculate EMA for remaining prices
                    for price in prices[period:]:
                        ema = (price * multiplier) + (ema * (1 - multiplier))

                    emas[period] = ema

            return emas

        except Exception as e:
            self.logger.error(f"Error in fallback EMA calculation: {e}")
            return {}

    def detect_crossover_signals(self, current_emas: Dict[int, float], previous_emas: Dict[int, float]) -> Optional[Dict]:
        """
        Detect EMA crossover signals with enhanced validation

        Args:
            current_emas: Current EMA values {period: value}
            previous_emas: Previous EMA values {period: value}

        Returns:
            Signal dictionary or None
        """
        try:
            if not current_emas or not previous_emas:
                return None

            if 5 not in current_emas or 10 not in current_emas:
                return None

            if 5 not in previous_emas or 10 not in previous_emas:
                return None

            # Current and previous EMA values
            ema5_current = current_emas[5]
            ema10_current = current_emas[10]
            ema5_previous = previous_emas[5]
            ema10_previous = previous_emas[10]

            # Get current price for validation
            current_price = self.price_data[-1]['price'] if self.price_data else ema5_current

            # Calculate percentage difference for noise filtering
            ema_diff_pct = abs(ema5_current - ema10_current) / current_price * 100
            min_diff_threshold = 0.05  # 0.05% minimum difference

            # Detect crossover
            signal_type = None
            crossover_strength = 0

            if ema5_previous <= ema10_previous and ema5_current > ema10_current:
                # Bullish crossover (Golden Cross)
                if ema_diff_pct >= min_diff_threshold:
                    signal_type = "BUY"
                    crossover_strength = (ema5_current - ema10_current) / current_price * 100
            elif ema5_previous >= ema10_previous and ema5_current < ema10_current:
                # Bearish crossover (Death Cross)
                if ema_diff_pct >= min_diff_threshold:
                    signal_type = "SELL"
                    crossover_strength = (ema10_current - ema5_current) / current_price * 100

            # Time-based filtering
            if signal_type:
                current_time = datetime.now()
                if (self.last_signal_time and
                    (current_time - self.last_signal_time).total_seconds() < self.min_signal_gap):
                    self.logger.debug(f"Signal filtered: too soon after last signal")
                    return None

                # Signal validation passed
                return {
                    'signal_type': signal_type,
                    'ema5': ema5_current,
                    'ema10': ema10_current,
                    'crossover_strength': crossover_strength,
                    'ema_diff_pct': ema_diff_pct,
                    'timestamp': current_time
                }

            return None

        except Exception as e:
            self.logger.error(f"Error detecting crossover signals: {e}")
            return None

    def process_tick_data(self, tick_data: Dict):
        """
        Process incoming tick data and detect signals

        Args:
            tick_data: Tick data from WebSocket
        """
        try:
            # Extract price and timestamp
            price = float(tick_data.get('LTP', 0))
            if price <= 0:
                return

            timestamp = datetime.now()
            volume = tick_data.get('volume', 1)

            # Store tick data
            self.price_data.append({
                'timestamp': timestamp,
                'price': price,
                'volume': volume
            })

            # Update statistics
            self.stats['ticks_received'] += 1
            self.stats['last_tick_time'] = timestamp

            # Calculate EMAs if we have enough data
            if len(self.price_data) >= self.min_data_points:
                prices = [item['price'] for item in self.price_data]
                current_emas = self.calculate_emas_pandas_ta(prices)

                if current_emas:
                    # Store EMA data
                    ema_entry = {
                        'timestamp': timestamp,
                        'price': price,
                        'emas': current_emas,
                        'data_points': len(self.price_data)
                    }
                    self.ema_data.append(ema_entry)

                    # Check for crossover signals
                    if len(self.ema_data) >= 2:
                        previous_emas = self.ema_data[-2]['emas']
                        signal = self.detect_crossover_signals(current_emas, previous_emas)

                        if signal:
                            self.handle_signal(signal, price, volume)

                    # Log to CSV every tick (for backtesting)
                    self.log_to_csv(timestamp, price, current_emas, None, volume)

            # Log progress every 100 ticks
            if self.stats['ticks_received'] % 100 == 0:
                self.logger.info(f"📊 Processed {self.stats['ticks_received']} ticks, "
                               f"Price: ₹{price:.2f}, Data points: {len(self.price_data)}")

        except Exception as e:
            self.logger.error(f"Error processing tick data: {e}")

    def handle_signal(self, signal: Dict, price: float, volume: int):
        """
        Handle detected crossover signal

        Args:
            signal: Signal dictionary
            price: Current price
            volume: Current volume
        """
        try:
            signal_type = signal['signal_type']
            ema5 = signal['ema5']
            ema10 = signal['ema10']
            strength = signal['crossover_strength']

            # Update signal tracking
            self.last_signal = signal_type
            self.last_signal_time = signal['timestamp']
            self.stats['signals_generated'] += 1

            # Log signal
            self.logger.info(f"🔔 {signal_type} SIGNAL DETECTED!")
            self.logger.info(f"   Price: ₹{price:.2f}")
            self.logger.info(f"   EMA5: {ema5:.2f}")
            self.logger.info(f"   EMA10: {ema10:.2f}")
            self.logger.info(f"   Strength: {strength:.3f}%")
            self.logger.info(f"   Time: {signal['timestamp'].strftime('%H:%M:%S')}")

            # Log to CSV with signal
            self.log_to_csv(signal['timestamp'], price, signal, signal_type, volume)

        except Exception as e:
            self.logger.error(f"Error handling signal: {e}")

    def log_to_csv(self, timestamp: datetime, price: float, emas: Dict,
                   signal_type: Optional[str], volume: int):
        """
        Log data to CSV file

        Args:
            timestamp: Data timestamp
            price: Current price
            emas: EMA values or signal dict
            signal_type: Signal type if any
            volume: Volume
        """
        try:
            with open(self.csv_file, 'a', newline='') as f:
                writer = csv.writer(f)

                if isinstance(emas, dict) and 'emas' in emas:
                    # EMA data entry
                    ema_values = emas['emas']
                    ema5 = ema_values.get(5, '')
                    ema10 = ema_values.get(10, '')
                    strength = ''
                    data_points = emas.get('data_points', len(self.price_data))
                elif isinstance(emas, dict) and 'signal_type' in emas:
                    # Signal entry
                    ema5 = emas.get('ema5', '')
                    ema10 = emas.get('ema10', '')
                    strength = emas.get('crossover_strength', '')
                    data_points = len(self.price_data)
                else:
                    # Regular EMA entry
                    ema5 = emas.get(5, '') if emas else ''
                    ema10 = emas.get(10, '') if emas else ''
                    strength = ''
                    data_points = len(self.price_data)

                writer.writerow([
                    timestamp.strftime('%Y-%m-%d %H:%M:%S'),
                    f"{price:.2f}",
                    f"{ema5:.2f}" if ema5 else '',
                    f"{ema10:.2f}" if ema10 else '',
                    signal_type or '',
                    f"{strength:.3f}" if strength else '',
                    volume,
                    data_points
                ])

        except Exception as e:
            self.logger.error(f"Error logging to CSV: {e}")

    async def connect_websocket(self):
        """Connect to DhanHQ WebSocket for live data"""
        try:
            self.logger.info("🔌 Connecting to DhanHQ WebSocket...")

            # Subscribe to instrument using DhanHQ API
            instrument_tokens = [self.config['instrument']['security_id']]

            # Subscribe to live feed
            response = self.dhan.live_feed_data(
                version=2,
                Instruments=instrument_tokens,
                Exchange_segment=self.config['instrument']['exchange_segment']
            )

            if response['status'] == 'success':
                self.logger.info("✅ Successfully subscribed to live feed")
                self.is_connected = True
                self.reconnect_attempts = 0

                # Start processing live data
                await self.process_live_feed()
            else:
                self.logger.error(f"❌ Failed to subscribe to live feed: {response}")
                await self.handle_reconnection()

        except Exception as e:
            self.logger.error(f"❌ WebSocket connection error: {e}")
            await self.handle_reconnection()

    async def process_live_feed(self):
        """Process live feed data from DhanHQ"""
        try:
            self.logger.info("📡 Starting live data processing...")

            while self.is_connected:
                try:
                    # Get live feed data from DhanHQ
                    # Note: DhanHQ uses polling method, not continuous WebSocket
                    live_data = self.dhan.live_feed_data(
                        version=2,
                        Instruments=[self.config['instrument']['security_id']],
                        Exchange_segment=self.config['instrument']['exchange_segment']
                    )

                    if live_data['status'] == 'success' and 'data' in live_data:
                        for tick in live_data['data']:
                            if 'LTP' in tick:
                                self.process_tick_data(tick)

                    # Sleep for a short interval (DhanHQ recommends not more than 1 request per second)
                    await asyncio.sleep(1)

                except Exception as e:
                    self.logger.error(f"Error in live feed processing: {e}")
                    await asyncio.sleep(5)

        except Exception as e:
            self.logger.error(f"Critical error in live feed processing: {e}")
            await self.handle_reconnection()

    async def handle_reconnection(self):
        """Handle WebSocket reconnection"""
        if self.reconnect_attempts >= self.max_reconnect_attempts:
            self.logger.error(f"❌ Max reconnection attempts ({self.max_reconnect_attempts}) reached")
            return

        self.reconnect_attempts += 1
        self.is_connected = False

        wait_time = min(30, 5 * self.reconnect_attempts)  # Exponential backoff, max 30s
        self.logger.info(f"🔄 Reconnection attempt {self.reconnect_attempts} in {wait_time} seconds...")

        await asyncio.sleep(wait_time)
        await self.connect_websocket()

    def print_statistics(self):
        """Print current statistics"""
        runtime = datetime.now() - self.stats['start_time']

        print(f"\n📊 Live Trading Statistics")
        print(f"=" * 50)
        print(f"Runtime: {runtime}")
        print(f"Ticks received: {self.stats['ticks_received']}")
        print(f"Signals generated: {self.stats['signals_generated']}")
        print(f"Data points stored: {len(self.price_data)}")
        print(f"EMA calculations: {len(self.ema_data)}")
        print(f"Last tick: {self.stats['last_tick_time']}")
        print(f"CSV file: {self.csv_file}")
        print(f"Connected: {self.is_connected}")

        if self.price_data:
            latest_price = self.price_data[-1]['price']
            print(f"Latest price: ₹{latest_price:.2f}")

        if self.ema_data:
            latest_emas = self.ema_data[-1]['emas']
            print(f"Latest EMA5: {latest_emas.get(5, 'N/A'):.2f}")
            print(f"Latest EMA10: {latest_emas.get(10, 'N/A'):.2f}")

        print(f"=" * 50)

    async def run(self):
        """Main execution loop"""
        try:
            self.logger.info("🚀 Starting Live EMA Trading System")
            self.logger.info(f"📊 Target: {self.config['instrument']['name']}")
            self.logger.info(f"📈 EMA Strategy: {self.ema_periods[0]}/{self.ema_periods[1]} crossover")
            self.logger.info(f"📁 Logging to: {self.csv_file}")

            # Connect to WebSocket
            await self.connect_websocket()

        except KeyboardInterrupt:
            self.logger.info("⚠️  Received interrupt signal, shutting down...")
            self.is_connected = False
        except Exception as e:
            self.logger.error(f"❌ Critical error in main loop: {e}")
            traceback.print_exc()
        finally:
            self.logger.info("🛑 Live trading system stopped")
            self.print_statistics()


async def main():
    """Main entry point"""
    try:
        # Create and run the live trading system
        trader = LiveEMATrader()
        await trader.run()

    except KeyboardInterrupt:
        print("\n⚠️  Interrupted by user")
    except Exception as e:
        print(f"❌ Error: {e}")
        traceback.print_exc()


if __name__ == "__main__":
    print("🚀 Live EMA Crossover Trading System")
    print("=" * 50)
    print("📊 Features:")
    print("   • Real-time NIFTY 50 data from DhanHQ")
    print("   • EMA 5/10 crossover detection")
    print("   • Enhanced signal validation")
    print("   • CSV logging for backtesting")
    print("   • Graceful error handling")
    print("=" * 50)
    print("⚠️  Press Ctrl+C to stop")
    print()

    # Ensure required directories exist
    os.makedirs("data", exist_ok=True)
    os.makedirs("logs", exist_ok=True)

    # Run the async main function
    asyncio.run(main())
