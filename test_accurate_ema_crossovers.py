#!/usr/bin/env python3
"""
Test Accurate EMA Crossover Detection for 1-minute NIFTY 50 Data
================================================================

This script tests the improved EMA crossover detection system with:
1. Real historical 1-minute data
2. Enhanced noise filtering
3. Time-based signal validation
4. Trend consistency checks

The goal is to generate accurate 5/10 EMA crossover signals that match
what a trader would see on a chart.
"""

import os
import sys
import csv
from datetime import datetime, timedelta
from typing import List, Dict

# Add src to path
sys.path.insert(0, 'src')

from core.ema import EMACalculator
from data.logger import SignalLogger


def load_historical_data(file_path: str, date_filter: str = None) -> List[Dict]:
    """Load historical 1-minute data from CSV without pandas"""
    try:
        data = []
        with open(file_path, 'r') as f:
            reader = csv.DictReader(f)
            for row in reader:
                try:
                    # Filter by date if specified
                    if date_filter and row['Date'] != date_filter:
                        continue

                    timestamp = datetime.strptime(f"{row['Date']} {row['Time']}", "%Y-%m-%d %H:%M:%S")
                    data.append({
                        'timestamp': timestamp,
                        'open': float(row['Open']),
                        'high': float(row['High']),
                        'low': float(row['Low']),
                        'close': float(row['Close']),
                        'volume': int(row['Volume'])
                    })
                except Exception as e:
                    print(f"Error parsing row: {e}")
                    continue

        return data

    except Exception as e:
        print(f"Error loading historical data: {e}")
        return []


def test_ema_crossover_accuracy():
    """Test EMA crossover detection with real data"""
    print("🧪 Testing Accurate EMA Crossover Detection for 1-minute Data")
    print("=" * 70)

    # Load historical data
    historical_file = "data/historical/nifty50_historical.csv"
    if not os.path.exists(historical_file):
        print(f"❌ Historical data file not found: {historical_file}")
        return False

    # Test with recent data (last 2 days)
    print("📊 Loading historical 1-minute data...")
    historical_data = load_historical_data(historical_file)

    if not historical_data:
        print("❌ No historical data loaded")
        return False

    print(f"✅ Loaded {len(historical_data)} historical data points")
    print(f"📅 Data range: {historical_data[0]['timestamp']} to {historical_data[-1]['timestamp']}")

    # Initialize EMA calculator with 5/10 combination
    ema_combinations = [{"short_ema": 5, "long_ema": 10}]
    calculator = EMACalculator(ema_combinations, max_history=2880)

    # Initialize signal logger for testing
    os.makedirs("test_results", exist_ok=True)
    logger = SignalLogger("test_results", initial_capital=100000)
    logger.recreate_daily_csv()

    print("\n🔄 Processing historical data and detecting crossovers...")

    # Process data and collect signals
    all_signals = []
    processed_count = 0

    # Use last 500 data points for testing (about 8 hours of 1-min data)
    test_data = historical_data[-500:] if len(historical_data) > 500 else historical_data

    for i, candle in enumerate(test_data):
        # Add price to EMA calculator
        emas = calculator.add_price("1min", candle['close'], candle['timestamp'])

        # Check for crossover signals
        signals = calculator.get_crossover_signals("1min")

        if signals:
            for signal in signals:
                print(f"\n🔔 Signal #{len(all_signals) + 1}: {signal['signal']} at {candle['timestamp']}")
                print(f"   Price: {candle['close']:.2f}")
                print(f"   EMA5: {signal['short_value']:.2f}")
                print(f"   EMA10: {signal['long_value']:.2f}")
                print(f"   Strength: {signal.get('crossover_strength', 0):.3f}%")
                print(f"   Diff: {signal.get('ema_diff_pct', 0):.3f}%")

                # Log signal to CSV
                signal_data = {
                    'datetime': candle['timestamp'],
                    'action': signal['signal'],
                    'price': candle['close'],
                    'ohlc': {
                        'open': candle['open'],
                        'high': candle['high'],
                        'low': candle['low'],
                        'close': candle['close'],
                        'volume': candle['volume']
                    },
                    'short_ema_value': signal['short_value'],
                    'long_ema_value': signal['long_value'],
                    'pnl': 0.0  # Will be calculated by logger
                }

                logger.log_signal(signal_data)
                all_signals.append(signal)

        processed_count += 1

        # Progress indicator
        if processed_count % 100 == 0:
            print(f"   Processed {processed_count}/{len(test_data)} data points...")

    print(f"\n📈 Processing complete!")
    print(f"   Total data points processed: {processed_count}")
    print(f"   Total signals detected: {len(all_signals)}")

    # Analyze signal quality
    if all_signals:
        print(f"\n📊 Signal Analysis:")
        buy_signals = [s for s in all_signals if s['signal'] == 'BUY']
        sell_signals = [s for s in all_signals if s['signal'] == 'SELL']

        print(f"   BUY signals: {len(buy_signals)}")
        print(f"   SELL signals: {len(sell_signals)}")

        # Calculate time gaps between signals
        if len(all_signals) > 1:
            time_gaps = []
            for i in range(1, len(all_signals)):
                # Get timestamps from test data
                prev_signal_time = None
                curr_signal_time = None

                for candle in test_data:
                    if abs((candle['timestamp'] - all_signals[i-1]['price']).total_seconds()) < 60:
                        prev_signal_time = candle['timestamp']
                    if abs((candle['timestamp'] - all_signals[i]['price']).total_seconds()) < 60:
                        curr_signal_time = candle['timestamp']

                if prev_signal_time and curr_signal_time:
                    gap_minutes = (curr_signal_time - prev_signal_time).total_seconds() / 60
                    time_gaps.append(gap_minutes)

            if time_gaps:
                avg_gap = sum(time_gaps) / len(time_gaps)
                min_gap = min(time_gaps)
                print(f"   Average time between signals: {avg_gap:.1f} minutes")
                print(f"   Minimum time between signals: {min_gap:.1f} minutes")

    # Get final statistics
    stats = logger.get_statistics()
    print(f"\n📋 Final Statistics:")
    print(f"   CSV file: {stats['csv_file']}")
    print(f"   Total signals logged: {stats['total_signals']}")

    logger.close()

    print(f"\n✅ Test completed successfully!")
    print(f"📁 Results saved to: test_results/")

    return True


def compare_with_original_csv():
    """Compare new results with original problematic CSV"""
    print("\n🔍 Comparing with original CSV...")

    original_file = "data/nifty50_ema_signals_20250530.csv"
    new_file = "test_results/nifty50_ema_signals_20250530.csv"

    if os.path.exists(original_file) and os.path.exists(new_file):
        try:
            # Count lines in original file
            with open(original_file, 'r') as f:
                original_lines = len(f.readlines()) - 1  # Exclude header

            # Count lines in new file
            with open(new_file, 'r') as f:
                new_lines = len(f.readlines()) - 1  # Exclude header

            print(f"   Original signals: {original_lines}")
            print(f"   New signals: {new_lines}")
            print(f"   Reduction: {original_lines - new_lines} signals")
            if original_lines > 0:
                print(f"   Improvement: {((original_lines - new_lines) / original_lines * 100):.1f}% fewer signals")

        except Exception as e:
            print(f"   Error comparing files: {e}")
    else:
        print(f"   Files not found for comparison")


if __name__ == "__main__":
    try:
        success = test_ema_crossover_accuracy()
        if success:
            compare_with_original_csv()

    except KeyboardInterrupt:
        print("\n⚠️  Test interrupted by user")
    except Exception as e:
        print(f"\n❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
