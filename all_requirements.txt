# NIFTY 50 EMA Crossover Trading System - Complete Requirements
# ============================================================
# This file contains ALL dependencies for the trading system
# Install with: uv pip install -r all_requirements.txt

# Core Python dependencies
websocket-client>=1.6.0
requests>=2.31.0
pytz>=2023.3
python-dateutil>=2.8.0
python-dotenv>=1.0.0

# Data processing and analysis (REQUIRED)
numpy>=1.21.0
pandas>=1.3.0

# Technical Analysis Libraries
# TA-Lib>=0.4.24  # Uncomment if TA-Lib is available (fastest option)
pandas-ta>=0.3.14b  # Alternative to TA-Lib for technical analysis

# DhanHQ API (REQUIRED for live trading)
dhanhq>=2.0.0  # Official DhanHQ library for live market data

# Data visualization (optional but useful)
matplotlib>=3.7.0
seaborn>=0.12.0
plotly>=5.15.0

# Enhanced data handling
openpyxl>=3.1.0  # For Excel file support
xlsxwriter>=3.1.0  # For Excel writing

# Logging and monitoring
colorlog>=6.7.0  # Colored logging output

# Development and testing
pytest>=7.4.0
pytest-cov>=4.1.0
black>=23.0.0
flake8>=6.0.0
pre-commit>=3.0.0

# Performance monitoring
psutil>=5.9.0  # System monitoring
memory-profiler>=0.61.0  # Memory usage tracking

# Configuration management
pyyaml>=6.0  # YAML config file support
configparser>=5.3.0  # INI config file support

# Database support (if needed)
sqlite3  # Built-in Python module
# sqlalchemy>=2.0.0  # Uncomment if using database

# Networking and API
urllib3>=2.0.0
certifi>=2023.0.0

# Time and scheduling
schedule>=1.2.0  # Task scheduling
croniter>=1.4.0  # Cron expression parsing

# Mathematical and statistical libraries
scipy>=1.11.0  # Scientific computing
scikit-learn>=1.3.0  # Machine learning (if needed for analysis)

# File handling
pathlib  # Built-in Python module
glob2>=0.7  # Enhanced file globbing

# Error handling and debugging
traceback  # Built-in Python module
pdb  # Built-in Python debugger
ipdb>=0.13.0  # Enhanced debugger

# Async support (if needed)
asyncio  # Built-in Python module
aiohttp>=3.8.0  # Async HTTP client

# Data validation
pydantic>=2.0.0  # Data validation and settings management

# Caching
cachetools>=5.3.0  # Caching utilities

# String and text processing
regex>=2023.0.0  # Enhanced regex support

# System utilities
click>=8.1.0  # Command line interface
rich>=13.0.0  # Rich text and beautiful formatting

# Backup and archiving
zipfile  # Built-in Python module
tarfile  # Built-in Python module

# Environment and system info
platform  # Built-in Python module
sys  # Built-in Python module
os  # Built-in Python module

# JSON handling
json  # Built-in Python module
jsonschema>=4.19.0  # JSON schema validation

# HTTP and web utilities
flask>=2.3.0  # Web framework (if building web interface)
fastapi>=0.103.0  # Modern API framework (alternative to Flask)
uvicorn>=0.23.0  # ASGI server for FastAPI

# Security
cryptography>=41.0.0  # Cryptographic recipes and primitives
bcrypt>=4.0.0  # Password hashing

# Monitoring and alerting
prometheus-client>=0.17.0  # Metrics collection
grafana-api>=1.0.0  # Grafana integration (if using)

# Email notifications (if needed)
smtplib  # Built-in Python module
email  # Built-in Python module

# SMS/WhatsApp notifications (if needed)
# twilio>=8.5.0  # Uncomment if using Twilio for notifications

# Cloud storage (if needed)
# boto3>=1.28.0  # AWS SDK (uncomment if using AWS)
# google-cloud-storage>=2.10.0  # Google Cloud Storage (uncomment if using GCP)

# Backup and recovery
shutil  # Built-in Python module
pickle  # Built-in Python module

# Performance optimization
numba>=0.58.0  # JIT compiler for numerical functions
cython>=3.0.0  # C extensions for Python

# Memory management
gc  # Built-in Python garbage collector
weakref  # Built-in Python weak references

# Threading and multiprocessing
threading  # Built-in Python module
multiprocessing  # Built-in Python module
concurrent.futures  # Built-in Python module

# Signal handling
signal  # Built-in Python module

# Random number generation
random  # Built-in Python module
secrets  # Built-in Python module

# Hash functions
hashlib  # Built-in Python module
hmac  # Built-in Python module

# Base64 encoding
base64  # Built-in Python module

# URL parsing
urllib.parse  # Built-in Python module

# Regular expressions
re  # Built-in Python module

# Collections
collections  # Built-in Python module
itertools  # Built-in Python module

# Functional programming
functools  # Built-in Python module
operator  # Built-in Python module

# Type hints
typing  # Built-in Python module
typing_extensions>=4.7.0

# Decimal arithmetic
decimal  # Built-in Python module
fractions  # Built-in Python module

# Copy operations
copy  # Built-in Python module

# Warnings
warnings  # Built-in Python module

# Inspection
inspect  # Built-in Python module

# Abstract base classes
abc  # Built-in Python module

# Context managers
contextlib  # Built-in Python module

# Enum support
enum  # Built-in Python module

# Data classes
dataclasses  # Built-in Python module (Python 3.7+)

# Temporary files
tempfile  # Built-in Python module

# Command line argument parsing
argparse  # Built-in Python module

# Configuration file parsing
configparser  # Built-in Python module

# Logging
logging  # Built-in Python module

# Unit testing
unittest  # Built-in Python module

# Documentation generation
# sphinx>=7.1.0  # Uncomment if generating documentation
# sphinx-rtd-theme>=1.3.0  # Read the Docs theme for Sphinx

# Code formatting and linting
autopep8>=2.0.0  # PEP 8 auto-formatter
pylint>=2.17.0  # Code analysis
mypy>=1.5.0  # Static type checker

# Git integration
gitpython>=3.1.0  # Git repository interaction

# Environment management
virtualenv>=20.24.0  # Virtual environment creation
pip-tools>=7.3.0  # Pip dependency management

# Package building
setuptools>=68.0.0  # Package building
wheel>=0.41.0  # Wheel format support
build>=0.10.0  # Build backend

# Version management
packaging>=23.1  # Version parsing and comparison
importlib-metadata>=6.8.0  # Package metadata

# Compatibility
six>=1.16.0  # Python 2/3 compatibility utilities

# Progress bars
tqdm>=4.66.0  # Progress bars for loops

# Pretty printing
pprint  # Built-in Python module

# Subprocess management
subprocess  # Built-in Python module

# Exit handling
atexit  # Built-in Python module

# Locale support
locale  # Built-in Python module

# Calendar utilities
calendar  # Built-in Python module

# Math functions
math  # Built-in Python module
cmath  # Built-in Python complex math

# Statistics
statistics  # Built-in Python module

# CSV handling
csv  # Built-in Python module

# XML processing
xml  # Built-in Python module
lxml>=4.9.0  # Enhanced XML processing

# HTML processing
html  # Built-in Python module
beautifulsoup4>=4.12.0  # HTML/XML parsing

# Image processing (if needed for charts)
# pillow>=10.0.0  # Uncomment if processing images

# Audio processing (if needed for alerts)
# pygame>=2.5.0  # Uncomment if using audio alerts

# Desktop notifications (if needed)
# plyer>=2.1.0  # Uncomment if using desktop notifications

# System tray integration (if needed)
# pystray>=0.19.0  # Uncomment if creating system tray app

# GUI framework (if building desktop app)
# tkinter  # Built-in Python module
# PyQt5>=5.15.0  # Uncomment if using PyQt5
# PyQt6>=6.5.0  # Uncomment if using PyQt6
# kivy>=2.2.0  # Uncomment if using Kivy

# Web scraping (if needed)
# scrapy>=2.10.0  # Uncomment if web scraping
# selenium>=4.11.0  # Uncomment if browser automation

# Machine learning (advanced features)
# tensorflow>=2.13.0  # Uncomment if using TensorFlow
# torch>=2.0.0  # Uncomment if using PyTorch
# xgboost>=1.7.0  # Uncomment if using XGBoost

# Natural language processing (if analyzing news)
# nltk>=3.8.0  # Uncomment if processing text
# spacy>=3.6.0  # Uncomment if advanced NLP

# Blockchain integration (if needed)
# web3>=6.9.0  # Uncomment if interacting with blockchain

# API rate limiting
# ratelimit>=2.2.0  # Uncomment if implementing rate limiting

# Caching backends
# redis>=4.6.0  # Uncomment if using Redis
# memcached>=1.59  # Uncomment if using Memcached

# Message queues (if needed)
# celery>=5.3.0  # Uncomment if using Celery
# rabbitmq>=0.2.0  # Uncomment if using RabbitMQ

# Monitoring and metrics
# statsd>=4.0.0  # Uncomment if using StatsD
# datadog>=0.47.0  # Uncomment if using Datadog

# Load testing (for API endpoints)
# locust>=2.16.0  # Uncomment if load testing

# Documentation and help
# sphinx-autodoc-typehints>=1.24.0  # Uncomment if using Sphinx with type hints
